# ATMA Frontend - AI-Driven Talent Mapping Assessment

A modern React frontend application for the ATMA (AI-Driven Talent Mapping Assessment) system, designed to provide comprehensive personality and talent assessment through AI-powered analysis.

## 🌟 Features

### Core Assessment System
- **RIASEC Assessment**: Realistic, Investigative, Artistic, Social, Enterprising, Conventional personality types
- **OCEAN (Big Five)**: Openness, Conscientiousness, Extraversion, Agreeableness, Neuroticism traits
- **VIA-IS Character Strengths**: 24 character strengths assessment
- **Real-time Processing**: Live status updates during AI analysis
- **Results Visualization**: Comprehensive analysis results with charts and insights

### User Management
- **Authentication System**: Secure login/register with JWT tokens
- **User Profiles**: Complete profile management with school integration
- **Token System**: Credit-based assessment submissions
- **Results History**: Access to all previous assessment results

### Admin Dashboard
- **User Management**: View, edit, and manage user accounts
- **Token Management**: Adjust user token balances
- **System Monitoring**: Health checks and service status
- **Analytics**: User statistics and system metrics

### Real-time Features
- **WebSocket Notifications**: Live updates for assessment completion
- **Connection Status**: Real-time backend connectivity monitoring
- **Progress Tracking**: Live assessment processing status

## 🏗️ Architecture

### API Gateway Integration
The frontend is designed to work with the ATMA API Gateway, providing:
- Single entry point for all backend services
- Centralized authentication and rate limiting
- Service discovery and load balancing
- WebSocket proxy for real-time notifications

### Service Integration
- **Auth Service**: User authentication and profile management
- **Assessment Service**: AI-powered assessment processing
- **Archive Service**: Results storage and retrieval
- **Notification Service**: Real-time updates via WebSocket

## 🚀 Getting Started

### Prerequisites
- Node.js 18+ 
- npm or yarn
- ATMA Backend Services (API Gateway, Auth, Assessment, Archive, Notification)

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/PLACE-HOLDERR423/Front-End.git
   cd Front-End
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Environment Configuration**
   ```bash
   cp .env.example .env
   ```
   
   Update `.env` with your configuration:
   ```env
   VITE_API_BASE_URL=http://localhost:3000
   VITE_NOTIFICATION_URL=http://localhost:3000
   VITE_APP_ENV=development
   ```

4. **Start the development server**
   ```bash
   npm run dev
   ```

5. **Access the application**
   Open [http://localhost:5173](http://localhost:5173) in your browser

## 🔧 Configuration

### API Endpoints
All API endpoints are configured in `src/config/api.js` and follow the API Gateway specification:

- **Authentication**: `/api/auth/*`
- **Assessment**: `/api/assessment/*`
- **Archive**: `/api/archive/*`
- **Admin**: `/api/admin/*`
- **Health**: `/health` and `/api/{service}/health`

### Environment Variables
- `VITE_API_BASE_URL`: API Gateway base URL (default: http://localhost:3000)
- `VITE_NOTIFICATION_URL`: WebSocket connection URL (default: http://localhost:3000)
- `VITE_APP_ENV`: Application environment (development/production)
- `VITE_DEBUG`: Enable debug logging (true/false)

## 🧪 Testing

### API Testing Scripts
- **Quick Test**: `npm run test:api:quick` - Basic API connectivity test
- **Full Test**: `npm run test:api:full` - Comprehensive API endpoint testing
- **Path Verification**: `npm run verify:api` - Verify API path configuration

### Manual Testing
- **Browser Test**: Open `public/test-api.html` for interactive API testing
- **Health Checks**: Visit `/health` endpoints for service status

## 📁 Project Structure

```
src/
├── components/          # React components
│   ├── Admin/          # Admin dashboard components
│   ├── Assessment/     # Assessment flow components
│   ├── Auth/           # Authentication components
│   ├── Dashboard/      # User dashboard components
│   ├── Layout/         # Layout and navigation components
│   ├── Profile/        # User profile components
│   ├── Results/        # Results display components
│   └── UI/             # Reusable UI components
├── config/             # Configuration files
├── context/            # React context providers
├── data/               # Static data and questions
├── hooks/              # Custom React hooks
├── services/           # API service classes
└── utils/              # Utility functions

scripts/                # Testing and utility scripts
public/                 # Static assets and test files
```

## 🔐 Security Features

- **JWT Authentication**: Secure token-based authentication
- **Protected Routes**: Route-level access control
- **API Rate Limiting**: Built-in rate limiting through API Gateway
- **Input Validation**: Comprehensive form validation
- **CORS Configuration**: Proper cross-origin resource sharing

## 🌐 Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## 📚 API Documentation

Detailed API documentation is available in `API_external.md`, including:
- Complete endpoint reference
- Authentication requirements
- Request/response examples
- Error handling
- Rate limiting information

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Check the API documentation in `API_external.md`
- Use health check endpoints for service status
- Review error logs in browser console
- Run verification scripts for configuration issues

## 🔄 Recent Updates

- ✅ Fixed all API endpoints to match API Gateway specification
- ✅ Added `/api` prefix to all service endpoints
- ✅ Updated assessment endpoints from `/assessments` to `/assessment`
- ✅ Configured WebSocket connections through API Gateway
- ✅ Added comprehensive admin dashboard
- ✅ Implemented real-time notifications
- ✅ Added API path verification tools
