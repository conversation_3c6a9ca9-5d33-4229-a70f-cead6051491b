#!/usr/bin/env node

/**
 * API Path Verification Script
 * Verifies that all API endpoints are correctly configured according to API_external.md
 */

import fs from 'fs';

// Read the API config file content
const configContent = fs.readFileSync('./src/config/api.js', 'utf8');

console.log('🔍 Verifying API Path Configuration');
console.log('='.repeat(50));

// Expected API paths according to API_external.md
const EXPECTED_PATHS = {
  // Auth endpoints should have /api prefix
  AUTH_LOGIN: '/api/auth/login',
  AUTH_REGISTER: '/api/auth/register',
  AUTH_PROFILE: '/api/auth/profile',
  AUTH_TOKEN_BALANCE: '/api/auth/token-balance',
  
  // Admin endpoints should have /api prefix
  ADMIN_LOGIN: '/api/admin/login',
  ADMIN_PROFILE: '/api/admin/profile',
  ADMIN_USERS: '/api/archive/admin/users',
  
  // Assessment endpoints should use /assessment (not /assessments)
  ASSESSMENT_SUBMIT: '/api/assessment/submit',
  ASSESSMENT_STATUS: '/api/assessment/status/{jobId}',
  
  // Archive endpoints should have /api prefix
  ARCHIVE_RESULTS: '/api/archive/results',
  ARCHIVE_STATS: '/api/archive/stats',
  
  // Health endpoints (gateway level, no /api prefix)
  HEALTH_MAIN: '/health',
  HEALTH_LIVE: '/health/live',
  
  // Service-specific health endpoints (with /api prefix)
  HEALTH_AUTH: '/api/auth/health',
  HEALTH_ARCHIVE: '/api/archive/health',
  HEALTH_ASSESSMENT: '/api/assessment/health',
};

// Verification functions
function verifyPath(description, actual, expected) {
  const isCorrect = actual === expected;
  const status = isCorrect ? '✅' : '❌';
  console.log(`${status} ${description}`);
  console.log(`   Expected: ${expected}`);
  console.log(`   Actual:   ${actual}`);
  if (!isCorrect) {
    console.log(`   ⚠️  MISMATCH DETECTED!`);
  }
  console.log('');
  return isCorrect;
}

function verifyFunctionPath(description, actualFunction, expectedPattern, testParam) {
  const actual = actualFunction(testParam);
  const expected = expectedPattern.replace('{jobId}', testParam).replace('{userId}', testParam).replace('{id}', testParam);
  return verifyPath(description, actual, expected);
}

// Check if the config file contains the correct patterns
function checkConfigPattern(pattern, description) {
  const found = configContent.includes(pattern);
  const status = found ? '✅' : '❌';
  console.log(`${status} ${description}: ${pattern}`);
  return found;
}

// Run verification
console.log('📋 Checking API Configuration File:');
console.log('');

let allCorrect = true;

// Verify Auth endpoints
console.log('🔐 Auth Endpoints:');
allCorrect &= checkConfigPattern("LOGIN: '/api/auth/login'", 'Auth Login');
allCorrect &= checkConfigPattern("REGISTER: '/api/auth/register'", 'Auth Register');
allCorrect &= checkConfigPattern("PROFILE: '/api/auth/profile'", 'Auth Profile');
allCorrect &= checkConfigPattern("TOKEN_BALANCE: '/api/auth/token-balance'", 'Auth Token Balance');

// Verify Admin endpoints
console.log('👨‍💼 Admin Endpoints:');
allCorrect &= checkConfigPattern("LOGIN: '/api/admin/login'", 'Admin Login');
allCorrect &= checkConfigPattern("PROFILE: '/api/admin/profile'", 'Admin Profile');
allCorrect &= checkConfigPattern("USERS: '/api/archive/admin/users'", 'Admin Users');

// Verify Assessment endpoints (should be /assessment not /assessments)
console.log('🎯 Assessment Endpoints:');
allCorrect &= checkConfigPattern("SUBMIT: '/api/assessment/submit'", 'Assessment Submit');
allCorrect &= checkConfigPattern("STATUS: (jobId) => `/api/assessment/status/${jobId}`", 'Assessment Status');

// Verify Archive endpoints
console.log('📊 Archive Endpoints:');
allCorrect &= checkConfigPattern("RESULTS: '/api/archive/results'", 'Archive Results');
allCorrect &= checkConfigPattern("STATS: '/api/archive/stats'", 'Archive Stats');

// Verify Health endpoints
console.log('🏥 Health Endpoints:');
allCorrect &= checkConfigPattern("MAIN: '/health'", 'Health Main');
allCorrect &= checkConfigPattern("LIVE: '/health/live'", 'Health Live');
allCorrect &= checkConfigPattern("AUTH: '/api/auth/health'", 'Health Auth');
allCorrect &= checkConfigPattern("ARCHIVE: '/api/archive/health'", 'Health Archive');
allCorrect &= checkConfigPattern("ASSESSMENT: '/api/assessment/health'", 'Health Assessment');

// Check that old incorrect paths are NOT present
console.log('🚫 Checking for old incorrect paths:');
const hasOldAuthPaths = configContent.includes("'/auth/login'") || configContent.includes("'/auth/register'");
const hasOldAssessmentPaths = configContent.includes("'/assessments/submit'") || configContent.includes("'/assessments/status'");
const hasOldArchivePaths = configContent.includes("'/archive/results'") && !configContent.includes("'/api/archive/results'");

if (hasOldAuthPaths) {
  console.log('❌ Found old auth paths without /api prefix');
  allCorrect = false;
} else {
  console.log('✅ No old auth paths found');
}

if (hasOldAssessmentPaths) {
  console.log('❌ Found old /assessments paths (should be /assessment)');
  allCorrect = false;
} else {
  console.log('✅ No old /assessments paths found');
}

if (hasOldArchivePaths) {
  console.log('❌ Found old archive paths without /api prefix');
  allCorrect = false;
} else {
  console.log('✅ No old archive paths found');
}

// Final result
console.log('='.repeat(50));
if (allCorrect) {
  console.log('🎉 All API paths are correctly configured!');
  console.log('✅ API endpoints match the API_external.md specification');
  process.exit(0);
} else {
  console.log('❌ Some API paths need correction!');
  console.log('⚠️  Please review the mismatches above and update the configuration');
  process.exit(1);
}
